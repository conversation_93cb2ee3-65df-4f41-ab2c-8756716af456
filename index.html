<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معالج PDF الذكي | أدوات معالجة ملفات PDF بالذكاء الاصطناعي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#10b981',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    },
                },
                fontFamily: {
                    sans: ['Tajawal', 'sans-serif'],
                }
            },
            darkMode: 'class',
        }

        // Check for dark mode preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        /* Custom file upload styling */
        .file-drop-area {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            border: 2px dashed #cbd5e1;
            border-radius: 0.5rem;
            background-color: #f8fafc;
            transition: all 0.3s ease;
        }

        .dark .file-drop-area {
            background-color: #1e293b;
            border-color: #475569;
        }

        .file-drop-area.is-active {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.05);
        }

        .dark .file-drop-area.is-active {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
        }

        /* File preview area */
        .file-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
            width: 100%;
        }

        .file-item {
            position: relative;
            width: 150px;
            padding: 0.5rem;
            border-radius: 0.25rem;
            background-color: #f1f5f9;
            border: 1px solid #cbd5e1;
            transition: all 0.2s ease;
        }

        .dark .file-item {
            background-color: #334155;
            border-color: #475569;
        }

        .file-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* PDF page thumbnails */
        .pdf-page {
            position: relative;
            width: 100px;
            height: 140px;
            margin: 0.5rem;
            border: 1px solid #cbd5e1;
            background-color: white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            cursor: move;
            transition: all 0.2s ease;
        }

        .dark .pdf-page {
            border-color: #475569;
            background-color: #1e293b;
        }

        .pdf-page:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .pdf-page-number {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            background-color: rgba(59, 130, 246, 0.8);
            color: white;
            padding: 0.25rem;
            font-size: 0.75rem;
        }

        /* Progress bar styling */
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .dark .progress-bar {
            background-color: #334155;
        }

        .progress-bar-fill {
            height: 100%;
            background-color: #3b82f6;
            transition: width 0.3s ease;
        }

        /* Hide scrollbar for Chrome, Safari and Opera */
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }

        /* Hide scrollbar for IE, Edge and Firefox */
        .no-scrollbar {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
    </style>
</head>
<body class="font-sans bg-light dark:bg-dark text-dark dark:text-light min-h-screen">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="#" class="flex items-center" id="home-link">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                        </svg>
                        <span class="mr-2 text-xl font-bold text-primary">معالج PDF الذكي</span>
                    </a>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-500 hover:text-primary focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                        </svg>
                    </button>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-1 space-x-reverse">
                    <a href="#" id="merge-link" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700">دمج ملفات PDF</a>
                    <a href="#" id="compress-link" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700">ضغط PDF</a>
                    <a href="#" id="reorder-link" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700">إعادة ترتيب الصفحات</a>
                    <a href="#" id="about-link" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700">من نحن</a>
                    <a href="#" id="contact-link" class="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700">اتصل بنا</a>
                </nav>
            </div>
            
            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <div class="flex flex-col space-y-2">
                    <a href="#" id="mobile-merge-link" class="px-3 py-2 rounded-md text-base font-medium hover:bg-gray-100 dark:hover:bg-gray-700">دمج ملفات PDF</a>
                    <a href="#" id="mobile-compress-link" class="px-3 py-2 rounded-md text-base font-medium hover:bg-gray-100 dark:hover:bg-gray-700">ضغط PDF</a>
                    <a href="#" id="mobile-reorder-link" class="px-3 py-2 rounded-md text-base font-medium hover:bg-gray-100 dark:hover:bg-gray-700">إعادة ترتيب الصفحات</a>
                    <a href="#" id="mobile-about-link" class="px-3 py-2 rounded-md text-base font-medium hover:bg-gray-100 dark:hover:bg-gray-700">من نحن</a>
                    <a href="#" id="mobile-contact-link" class="px-3 py-2 rounded-md text-base font-medium hover:bg-gray-100 dark:hover:bg-gray-700">اتصل بنا</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Home Page -->
        <div id="home-page" class="page active">
            <section class="text-center max-w-4xl mx-auto">
                <h1 class="text-4xl font-bold text-primary mb-4">أدوات معالجة PDF بتقنية الذكاء الاصطناعي</h1>
                <p class="text-lg mb-8">حلول سهلة وفعالة لإدارة ملفات PDF الخاصة بك، مدعومة بأحدث تقنيات الذكاء الاصطناعي لتحسين الجودة والكفاءة.</p>
                
                <div class="grid md:grid-cols-3 gap-6 mb-12">
                    <!-- Merge Tool Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden transition-transform hover:scale-105">
                        <div class="p-4 bg-blue-50 dark:bg-blue-900/30">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-primary mx-auto mb-2" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                            </svg>
                            <h3 class="text-xl font-semibold mb-2">دمج ملفات PDF</h3>
                        </div>
                        <div class="p-6">
                            <p class="mb-4">دمج عدة ملفات PDF في ملف واحد بسهولة. يقوم الذكاء الاصطناعي بتحسين جودة الصفحات وتنظيمها.</p>
                            <button id="home-merge-btn" class="w-full bg-primary hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition-colors">
                                استخدام الأداة
                            </button>
                        </div>
                    </div>
                    
                    <!-- Compress Tool Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden transition-transform hover:scale-105">
                        <div class="p-4 bg-green-50 dark:bg-green-900/30">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-secondary mx-auto mb-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            <h3 class="text-xl font-semibold mb-2">ضغط PDF</h3>
                        </div>
                        <div class="p-6">
                            <p class="mb-4">تقليل حجم ملفات PDF مع الحفاظ على الجودة. يستخدم الذكاء الاصطناعي لتحسين نسبة الضغط.</p>
                            <button id="home-compress-btn" class="w-full bg-secondary hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition-colors">
                                استخدام الأداة
                            </button>
                        </div>
                    </div>
                    
                    <!-- Reorder Tool Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden transition-transform hover:scale-105">
                        <div class="p-4 bg-purple-50 dark:bg-purple-900/30">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-purple-500 mx-auto mb-2" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                            </svg>
                            <h3 class="text-xl font-semibold mb-2">إعادة ترتيب الصفحات</h3>
                        </div>
                        <div class="p-6">
                            <p class="mb-4">تغيير ترتيب صفحات ملف PDF بسهولة عن طريق السحب والإفلات. يساعد الذكاء الاصطناعي في تنظيم المحتوى.</p>
                            <button id="home-reorder-btn" class="w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition-colors">
                                استخدام الأداة
                            </button>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- AI Features -->
            <section class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-8 mb-12">
                <h2 class="text-2xl font-bold text-center mb-8">مميزات الذكاء الاصطناعي</h2>
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="bg-white dark:bg-gray-800 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">تحسين الجودة</h3>
                        <p>يقوم الذكاء الاصطناعي بتحسين جودة النص والصور في ملفات PDF الخاصة بك تلقائيًا.</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-white dark:bg-gray-800 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">حماية البيانات</h3>
                        <p>تتم معالجة جميع الملفات باستخدام تقنيات أمان متقدمة لضمان خصوصية بياناتك.</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-white dark:bg-gray-800 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">تحسين مستمر</h3>
                        <p>تتعلم خوارزميات الذكاء الاصطناعي من الاستخدام لتقديم نتائج أفضل في كل مرة.</p>
                    </div>
                </div>
            </section>
            
            <!-- FAQ Section -->
            <section class="max-w-3xl mx-auto mb-12">
                <h2 class="text-2xl font-bold text-center mb-8">الأسئلة الشائعة</h2>
                <div class="space-y-4">
                    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                        <button class="faq-question w-full text-right font-semibold flex justify-between items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            <span>كيف يعمل الذكاء الاصطناعي مع ملفات PDF؟</span>
                        </button>
                        <div class="faq-answer hidden mt-2 text-gray-600 dark:text-gray-300">
                            <p>يستخدم الذكاء الاصطناعي في أدواتنا لتحليل محتوى الملفات وتحسين جودة النص والصور. كما يقوم بتحسين عملية الضغط للحفاظ على الجودة مع تقليل حجم الملف، وتنظيم الصفحات بشكل أكثر منطقية عند دمج ملفات متعددة.</p>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                        <button class="faq-question w-full text-right font-semibold flex justify-between items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            <span>هل تحفظ الموقع ملفات PDF الخاصة بي؟</span>
                        </button>
                        <div class="faq-answer hidden mt-2 text-gray-600 dark:text-gray-300">
                            <p>لا، لا نقوم بتخزين ملفاتك على خوادمنا بعد المعالجة. تتم معالجة جميع الملفات في المتصفح الخاص بك، وبمجرد إغلاق الصفحة أو تحميل الملف الناتج، يتم مسح جميع البيانات المؤقتة.</p>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                        <button class="faq-question w-full text-right font-semibold flex justify-between items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            <span>ما هو الحد الأقصى لحجم الملفات التي يمكن معالجتها؟</span>
                        </button>
                        <div class="faq-answer hidden mt-2 text-gray-600 dark:text-gray-300">
                            <p>يمكن معالجة ملفات بحجم يصل إلى 50 ميجابايت لكل ملف. إذا كنت تحتاج إلى معالجة ملفات أكبر، يرجى الاتصال بنا للحصول على حلول مخصصة.</p>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                        <button class="faq-question w-full text-right font-semibold flex justify-between items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            <span>هل الأدوات مجانية للاستخدام؟</span>
                        </button>
                        <div class="faq-answer hidden mt-2 text-gray-600 dark:text-gray-300">
                            <p>نعم، جميع الأدوات الأساسية مجانية للاستخدام. نقدم أيضًا خطة اشتراك مميزة تتيح معالجة ملفات أكبر ومزايا إضافية لمستخدمي الأعمال والمؤسسات.</p>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Testimonials -->
            <section class="max-w-4xl mx-auto">
                <h2 class="text-2xl font-bold text-center mb-8">آراء المستخدمين</h2>
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                        <div class="flex text-yellow-400 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                        <p class="mb-4">أداة رائعة وسهلة الاستخدام! ساعدتني كثيرًا في تنظيم وثائق العمل وتقليل حجمها للمشاركة مع العملاء.</p>
                        <p class="font-semibold">محمد الأحمد</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">مدير تسويق</p>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                        <div class="flex text-yellow-400 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                        <p class="mb-4">كطالبة، أحتاج دائمًا إلى دمج ملفات PDF للمشاريع والأبحاث. هذه الأداة سهلت علي العملية كثيرًا وحسنت جودة المستندات.</p>
                        <p class="font-semibold">سارة العلي</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">طالبة جامعية</p>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                        <div class="flex text-yellow-400 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                        <p class="mb-4">نستخدم هذه الأداة في شركتنا بشكل يومي لإدارة المستندات. تقنية الذكاء الاصطناعي تحافظ على جودة الملفات حتى بعد الضغط.</p>
                        <p class="font-semibold">أحمد الخالدي</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">مدير تقنية المعلومات</p>
                    </div>
                </div>
            </section>
        </div>
        
        <!-- PDF Merge Tool Page -->
        <div id="merge-page" class="page hidden">
            <h1 class="text-3xl font-bold text-primary mb-6">دمج ملفات PDF</h1>
            <p class="mb-8">اختر ملفات PDF متعددة لدمجها في ملف واحد. يمكنك إعادة ترتيب الملفات قبل الدمج.</p>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <!-- File Upload Area -->
                <div id="merge-drop-area" class="file-drop-area mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-lg mb-2">اسحب وأفلت ملفات PDF هنا</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">أو</p>
                    <label class="bg-primary hover:bg-blue-600 text-white font-bold py-2 px-4 rounded cursor-pointer transition-colors">
                        اختر الملفات
                        <input type="file" id="merge-file-input" class="hidden" multiple accept=".pdf">
                    </label>
                </div>
                
                <!-- File Preview Area -->
                <div id="merge-preview" class="hidden">
                    <h3 class="text-lg font-semibold mb-4">الملفات المحددة</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">اسحب وأفلت لإعادة الترتيب</p>
                    
                    <div id="merge-file-list" class="file-preview mb-6"></div>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-end">
                        <button id="clear-merge-files" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition-colors">
                            مسح الكل
                        </button>
                        <button id="merge-pdf-button" class="bg-primary hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition-colors">
                            دمج الملفات
                        </button>
                    </div>
                </div>
                
                <!-- Processing State -->
                <div id="merge-processing" class="hidden text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
                    <p class="text-lg mb-2">جاري معالجة الملفات...</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">يقوم الذكاء الاصطناعي بتحسين جودة الملفات قبل الدمج</p>
                    
                    <div class="max-w-md mx-auto mt-6">
                        <div class="progress-bar">
                            <div id="merge-progress-bar" class="progress-bar-fill" style="width: 0%"></div>
                        </div>
                        <p id="merge-progress-text" class="text-sm mt-2">0%</p>
                    </div>
                </div>
                
                <!-- Success State -->
                <div id="merge-success" class="hidden text-center py-8">
                    <div class="inline-block rounded-full h-16 w-16 bg-green-100 dark:bg-green-900/30 text-secondary mb-4 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <p class="text-lg mb-4">تم دمج الملفات بنجاح!</p>
                    <button id="merge-download-btn" class="bg-secondary hover:bg-green-600 text-white font-bold py-2 px-6 rounded transition-colors">
                        تحميل الملف
                    </button>
                    <button id="merge-new-btn" class="block mx-auto mt-4 text-primary hover:underline">
                        دمج ملفات أخرى
                    </button>
                </div>
            </div>
            
            <!-- AI Features -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4">كيف يساعد الذكاء الاصطناعي في دمج الملفات</h3>
                <ul class="space-y-2 list-disc list-inside">
                    <li>تحسين جودة النص والصور في الملفات المدمجة</li>
                    <li>تنظيم الصفحات بشكل منطقي وفقًا للمحتوى</li>
                    <li>ضبط حجم الصفحات للتأكد من تناسقها في الملف النهائي</li>
                    <li>تحسين قابلية البحث في النص ضمن الملف المدمج</li>
                </ul>
            </div>
        </div>
        
        <!-- PDF Compress Tool Page -->
        <div id="compress-page" class="page hidden">
            <h1 class="text-3xl font-bold text-secondary mb-6">ضغط ملفات PDF</h1>
            <p class="mb-8">قم بتقليل حجم ملف PDF مع الحفاظ على جودة المحتوى باستخدام تقنيات الذكاء الاصطناعي.</p>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <!-- File Upload Area -->
                <div id="compress-drop-area" class="file-drop-area mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-lg mb-2">اسحب وأفلت ملف PDF هنا</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">أو</p>
                    <label class="bg-secondary hover:bg-green-600 text-white font-bold py-2 px-4 rounded cursor-pointer transition-colors">
                        اختر ملف
                        <input type="file" id="compress-file-input" class="hidden" accept=".pdf">
                    </label>
                </div>
                
                <!-- Compression Options -->
                <div id="compress-options" class="hidden mb-6">
                    <h3 class="text-lg font-semibold mb-4">ملف: <span id="compress-filename" class="font-normal"></span></h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">الحجم الأصلي: <span id="compress-filesize" class="font-semibold"></span></p>
                    
                    <div class="mb-6">
                        <label class="block text-lg font-semibold mb-2">مستوى الضغط</label>
                        <div class="flex flex-wrap gap-4">
                            <label class="relative flex items-center cursor-pointer">
                                <input type="radio" name="compression-level" value="low" class="sr-only peer" checked>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-500"></div>
                                <span class="mr-3 text-sm font-medium text-gray-900 dark:text-gray-300">منخفض (جودة عالية)</span>
                            </label>
                            
                            <label class="relative flex items-center cursor-pointer">
                                <input type="radio" name="compression-level" value="medium" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-500"></div>
                                <span class="mr-3 text-sm font-medium text-gray-900 dark:text-gray-300">متوسط (مناسب للاستخدام العام)</span>
                            </label>
                            
                            <label class="relative flex items-center cursor-pointer">
                                <input type="radio" name="compression-level" value="high" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-500"></div>
                                <span class="mr-3 text-sm font-medium text-gray-900 dark:text-gray-300">مرتفع (حجم صغير)</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-end">
                        <button id="clear-compress-file" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition-colors">
                            إلغاء
                        </button>
                        <button id="compress-pdf-button" class="bg-secondary hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition-colors">
                            ضغط الملف
                        </button>
                    </div>
                </div>
                
                <!-- Processing State -->
                <div id="compress-processing" class="hidden text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-secondary mb-4"></div>
                    <p class="text-lg mb-2">جاري ضغط الملف...</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">يقوم الذكاء الاصطناعي بتحسين الضغط مع الحفاظ على الجودة</p>
                    
                    <div class="max-w-md mx-auto mt-6">
                        <div class="progress-bar">
                            <div id="compress-progress-bar" class="progress-bar-fill bg-secondary" style="width: 0%"></div>
                        </div>
                        <p id="compress-progress-text" class="text-sm mt-2">0%</p>
                    </div>
                </div>
                
                <!-- Success State -->
                <div id="compress-success" class="hidden text-center py-8">
                    <div class="inline-block rounded-full h-16 w-16 bg-green-100 dark:bg-green-900/30 text-secondary mb-4 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <p class="text-lg mb-2">تم ضغط الملف بنجاح!</p>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-w-sm mx-auto mb-6">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-500 dark:text-gray-400">الحجم الأصلي:</span>
                            <span id="original-size" class="font-semibold">1.2 MB</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-500 dark:text-gray-400">الحجم الجديد:</span>
                            <span id="new-size" class="font-semibold">0.5 MB</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500 dark:text-gray-400">نسبة التخفيض:</span>
                            <span id="reduction-percentage" class="font-semibold text-secondary">58%</span>
                        </div>
                    </div>
                    
                    <button id="compress-download-btn" class="bg-secondary hover:bg-green-600 text-white font-bold py-2 px-6 rounded transition-colors">
                        تحميل الملف
                    </button>
                    <button id="compress-new-btn" class="block mx-auto mt-4 text-secondary hover:underline">
                        ضغط ملف آخر
                    </button>
                </div>
            </div>
            
            <!-- AI Features -->
            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4">كيف يساعد الذكاء الاصطناعي في ضغط الملفات</h3>
                <ul class="space-y-2 list-disc list-inside">
                    <li>تحليل محتوى الملف لاختيار أفضل طريقة ضغط لكل جزء (نص، صور، رسومات)</li>
                    <li>تحسين جودة الصور مع تقليل حجمها بطريقة ذكية</li>
                    <li>إزالة البيانات غير المرئية والغير ضرورية دون التأثير على المحتوى</li>
                    <li>الحفاظ على إمكانية البحث في النص حتى بعد الضغط</li>
                </ul>
            </div>
        </div>
        
        <!-- PDF Reorder Pages Tool Page -->
        <div id="reorder-page" class="page hidden">
            <h1 class="text-3xl font-bold text-purple-500 mb-6">إعادة ترتيب صفحات PDF</h1>
            <p class="mb-8">قم بتغيير ترتيب صفحات ملف PDF بسهولة عن طريق السحب والإفلات أو إدخال الترتيب يدويًا.</p>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <!-- File Upload Area -->
                <div id="reorder-drop-area" class="file-drop-area mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-lg mb-2">اسحب وأفلت ملف PDF هنا</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">أو</p>
                    <label class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded cursor-pointer transition-colors">
                        اختر ملف
                        <input type="file" id="reorder-file-input" class="hidden" accept=".pdf">
                    </label>
                </div>
                
                <!-- Page Preview Area -->
                <div id="reorder-preview" class="hidden">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">الصفحات: <span id="reorder-pagecount" class="font-normal"></span></h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">اسحب وأفلت لإعادة الترتيب</p>
                    </div>
                    
                    <div id="pages-container" class="flex flex-wrap gap-4 mb-6 items-start"></div>
                    
                    <div class="mb-6">
                        <label for="manual-order" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            إدخال الترتيب يدويًا (مثال: 1,3,2,4)
                        </label>
                        <div class="flex">
                            <input type="text" id="manual-order" class="bg-gray-50 border border-gray-300 text-gray-900 text-base rounded-lg focus:ring-purple-500 focus:border-purple-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-purple-500 dark:focus:border-purple-500 ml-2" placeholder="1,2,3,4,...">
                            <button id="apply-manual-order" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition-colors">
                                تطبيق
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-end">
                        <button id="clear-reorder-file" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition-colors">
                            إلغاء
                        </button>
                        <button id="reorder-pdf-button" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition-colors">
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
                
                <!-- Processing State -->
                <div id="reorder-processing" class="hidden text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
                    <p class="text-lg mb-2">جاري إعادة ترتيب الصفحات...</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">يقوم الذكاء الاصطناعي بتحسين المحتوى أثناء المعالجة</p>
                    
                    <div class="max-w-md mx-auto mt-6">
                        <div class="progress-bar">
                            <div id="reorder-progress-bar" class="progress-bar-fill bg-purple-500" style="width: 0%"></div>
                        </div>
                        <p id="reorder-progress-text" class="text-sm mt-2">0%</p>
                    </div>
                </div>
                
                <!-- Success State -->
                <div id="reorder-success" class="hidden text-center py-8">
                    <div class="inline-block rounded-full h-16 w-16 bg-purple-100 dark:bg-purple-900/30 text-purple-500 mb-4 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <p class="text-lg mb-4">تم إعادة ترتيب الصفحات بنجاح!</p>
                    <button id="reorder-download-btn" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-6 rounded transition-colors">
                        تحميل الملف
                    </button>
                    <button id="reorder-new-btn" class="block mx-auto mt-4 text-purple-500 hover:underline">
                        معالجة ملف آخر
                    </button>
                </div>
            </div>
            
            <!-- AI Features -->
            <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4">كيف يساعد الذكاء الاصطناعي في إعادة ترتيب الصفحات</h3>
                <ul class="space-y-2 list-disc list-inside">
                    <li>تحليل محتوى الصفحات واقتراح ترتيب منطقي بناءً على الموضوعات والعناوين</li>
                    <li>تصحيح اتجاه الصفحات تلقائيًا وتدوير الصفحات المقلوبة</li>
                    <li>تحسين جودة الصفحات أثناء عملية إعادة الترتيب</li>
                    <li>اكتشاف وإصلاح مشاكل التنسيق المحتملة بعد إعادة الترتيب</li>
                    <li>تحليل التسلسل المنطقي للمستند واقتراح أفضل ترتيب للصفحات</li>
                    <li>التعرف الذكي على أرقام الصفحات وترتيبها بشكل تلقائي</li>
                </ul>
            </div>
        </div>
        
        <!-- About Us Page -->
        <div id="about-page" class="page hidden">
            <h1 class="text-3xl font-bold text-primary mb-6">من نحن</h1>
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <p class="mb-4">نحن فريق من المطورين والمصممين المتخصصين في تقنيات الذكاء الاصطناعي وتطبيقاتها في معالجة المستندات. هدفنا هو تقديم أدوات سهلة الاستخدام وفعالة لتحسين تجربة المستخدمين في التعامل مع ملفات PDF.</p>
                    <p class="mb-4">تم تأسيس مشروعنا في عام 2023، وقد قمنا بتطوير منصة متكاملة تجمع بين تقنيات معالجة المستندات وأحدث خوارزميات الذكاء الاصطناعي لتقديم حلول مبتكرة.</p>
                    <p class="mb-4">نؤمن بأن التكنولوجيا يجب أن تكون متاحة للجميع، لذلك نقدم أدواتنا الأساسية مجانًا مع ضمان أعلى معايير الخصوصية والأمان لجميع المستخدمين.</p>
                    
                    <h2 class="text-xl font-bold mb-3 mt-6">مهمتنا</h2>
                    <p class="mb-6">تسهيل وتحسين عملية إدارة المستندات الرقمية من خلال توظيف تقنيات الذكاء الاصطناعي بطريقة مبسطة ومتاحة للجميع.</p>
                    
                    <h2 class="text-xl font-bold mb-3">قيمنا</h2>
                    <ul class="space-y-2 list-disc list-inside mb-6">
                        <li>الابتكار المستمر في مجال معالجة المستندات</li>
                        <li>سهولة الاستخدام وتجربة مستخدم متميزة</li>
                        <li>خصوصية وأمان البيانات</li>
                        <li>الجودة العالية في جميع خدماتنا</li>
                    </ul>
                </div>
                
                <div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                        <h2 class="text-xl font-bold mb-4">تقنية الذكاء الاصطناعي</h2>
                        <p class="mb-4">نستخدم أحدث تقنيات التعلم العميق لتحسين عمليات معالجة ملفات PDF:</p>
                        <ul class="space-y-3 mb-4">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary ml-2 mt-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>خوارزميات تحسين الصور للحفاظ على الجودة أثناء الضغط</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary ml-2 mt-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>تقنيات التعرف الضوئي على النصوص (OCR) لتحسين قابلية البحث</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary ml-2 mt-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>خوارزميات تحليل المحتوى لتنظيم الصفحات بشكل منطقي</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary ml-2 mt-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>نماذج معالجة اللغة الطبيعية لفهم محتوى المستندات</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                        <h2 class="text-xl font-bold mb-4">أمان وخصوصية البيانات</h2>
                        <p class="mb-4">نلتزم بأعلى معايير الأمان والخصوصية:</p>
                        <ul class="space-y-2 list-disc list-inside">
                            <li>جميع عمليات معالجة الملفات تتم في متصفح المستخدم</li>
                            <li>لا نخزن أي ملفات على خوادمنا بعد المعالجة</li>
                            <li>تشفير البيانات أثناء عملية المعالجة</li>
                            <li>عدم مشاركة أي بيانات مع أطراف ثالثة</li>
                            <li>امتثال كامل لمعايير الخصوصية العالمية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact Us Page -->
        <div id="contact-page" class="page hidden">
            <h1 class="text-3xl font-bold text-primary mb-6">اتصل بنا</h1>
            <p class="mb-8">نحن هنا للإجابة على أسئلتك واستفساراتك. يرجى ملء النموذج أدناه وسنرد عليك في أقرب وقت ممكن.</p>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <form id="contact-form" class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم</label>
                            <input type="text" id="name" class="bg-gray-50 border border-gray-300 text-gray-900 text-base rounded-lg focus:ring-primary focus:border-primary block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary dark:focus:border-primary" placeholder="أدخل اسمك الكامل" required>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني</label>
                            <input type="email" id="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-base rounded-lg focus:ring-primary focus:border-primary block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary dark:focus:border-primary" placeholder="<EMAIL>" required>
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموضوع</label>
                            <input type="text" id="subject" class="bg-gray-50 border border-gray-300 text-gray-900 text-base rounded-lg focus:ring-primary focus:border-primary block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary dark:focus:border-primary" placeholder="أدخل موضوع الرسالة" required>
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الرسالة</label>
                            <textarea id="message" rows="4" class="bg-gray-50 border border-gray-300 text-gray-900 text-base rounded-lg focus:ring-primary focus:border-primary block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary dark:focus:border-primary" placeholder="اكتب رسالتك هنا..." required></textarea>
                        </div>
                        
                        <button type="submit" class="w-full bg-primary hover:bg-blue-600 text-white font-bold py-2.5 px-5 rounded transition-colors">
                            إرسال
                        </button>
                    </form>
                    
                    <div id="contact-success" class="hidden mt-6 p-4 rounded-lg bg-green-50 dark:bg-green-900/20 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto text-secondary mb-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <p class="text-lg font-semibold mb-1">تم إرسال رسالتك بنجاح!</p>
                        <p class="text-sm">سنرد عليك في أقرب وقت ممكن. شكرًا لتواصلك معنا.</p>
                    </div>
                </div>
                
                <div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                        <h2 class="text-xl font-bold mb-4">معلومات الاتصال</h2>
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary ml-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <div>
                                    <p class="font-semibold">البريد الإلكتروني</p>
                                    <p class="text-gray-600 dark:text-gray-300"><EMAIL></p>
                                    <p class="text-gray-600 dark:text-gray-300 mt-1">شهيد موسى - منشئ الموقع</p>
                                </div>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary ml-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <div>
                                    <p class="font-semibold">الهاتف</p>
                                    <p class="text-gray-600 dark:text-gray-300">+966508658114</p>
                                    <p class="text-gray-600 dark:text-gray-300 mt-1">شهيد موسى</p>
                                </div>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary ml-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div>
                                    <p class="font-semibold">ساعات العمل</p>
                                    <p class="text-gray-600 dark:text-gray-300">الأحد - الخميس: 9:00 ص - 5:00 م</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                        <h2 class="text-xl font-bold mb-4">الأسئلة الشائعة</h2>
                        <p class="mb-4">قد تجد إجابات لأسئلتك في قسم الأسئلة الشائعة:</p>
                        <a href="#" id="faq-link" class="inline-flex items-center text-primary hover:underline">
                            الانتقال إلى الأسئلة الشائعة
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 shadow-inner mt-12">
        <div class="container mx-auto px-4 py-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-bold mb-4">معالج PDF الذكي</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">أدوات معالجة ملفات PDF مدعومة بالذكاء الاصطناعي لتحسين الجودة والكفاءة.</p>
                    <p class="text-gray-600 dark:text-gray-300">تم التطوير بواسطة: <span class="font-semibold">شهيد موسى</span></p>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="#" id="footer-merge-link" class="text-gray-600 dark:text-gray-300 hover:text-primary">دمج ملفات PDF</a></li>
                        <li><a href="#" id="footer-compress-link" class="text-gray-600 dark:text-gray-300 hover:text-primary">ضغط PDF</a></li>
                        <li><a href="#" id="footer-reorder-link" class="text-gray-600 dark:text-gray-300 hover:text-primary">إعادة ترتيب الصفحات</a></li>
                        <li><a href="#" id="footer-about-link" class="text-gray-600 dark:text-gray-300 hover:text-primary">من نحن</a></li>
                        <li><a href="#" id="footer-contact-link" class="text-gray-600 dark:text-gray-300 hover:text-primary">اتصل بنا</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">الخصوصية</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-300 hover:text-primary">سياسة الخصوصية</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-300 hover:text-primary">شروط الاستخدام</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-300 hover:text-primary">سياسة ملفات تعريف الارتباط</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-300 hover:text-primary">الأمان</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">تواصل معنا</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary ml-2" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300"><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary ml-2" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300">+966508658114</span>
                        </li>
                        <li class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary ml-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 005 10a1 1 0 112 0c0 .535-.11 1.043-.308 1.504A3 3 0 018 10a1 1 0 112 0c0 .378-.074.733-.205 1.062A1.996 1.996 0 0110 11a1 1 0 112 0c0 .513-.196.983-.516 1.336A2.96 2.96 0 0111 13a1 1 0 112 0c0 .341-.059.672-.17.985 1.05-.621 2.327-1.723 2.654-3.241A1 1 0 0114 10a1 1 0 112 0c0 .635-.123 1.244-.346 1.8a1 1 0 01-.793.619c-.935.196-1.939.171-2.861-.245z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300">شهيد موسى</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-6 text-center">
                <p class="text-gray-500 dark:text-gray-400">&copy; 2023 معالج PDF الذكي. جميع الحقوق محفوظة لـ شهيد موسى.</p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize page navigation
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation variables
            const pages = document.querySelectorAll('.page');
            const navLinks = {
                home: ['home-link', 'footer-home-link'],
                merge: ['merge-link', 'mobile-merge-link', 'footer-merge-link', 'home-merge-btn'],
                compress: ['compress-link', 'mobile-compress-link', 'footer-compress-link', 'home-compress-btn'],
                reorder: ['reorder-link', 'mobile-reorder-link', 'footer-reorder-link', 'home-reorder-btn'],
                about: ['about-link', 'mobile-about-link', 'footer-about-link'],
                contact: ['contact-link', 'mobile-contact-link', 'footer-contact-link']
            };
            
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            
            mobileMenuButton.addEventListener('click', function() {
                const isHidden = mobileMenu.classList.contains('hidden');
                mobileMenu.classList.toggle('hidden', !isHidden);
            });
            
            // Navigation functions
            function showPage(pageId) {
                pages.forEach(page => {
                    page.classList.add('hidden');
                    page.classList.remove('active');
                });
                
                const targetPage = document.getElementById(`${pageId}-page`);
                if (targetPage) {
                    targetPage.classList.remove('hidden');
                    targetPage.classList.add('active');
                    window.scrollTo(0, 0);
                }
                
                // Close mobile menu if open
                mobileMenu.classList.add('hidden');
            }
            
            // Set up navigation links
            Object.entries(navLinks).forEach(([page, ids]) => {
                ids.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.addEventListener('click', function(e) {
                            e.preventDefault();
                            showPage(page);
                        });
                    }
                });
            });
            
            // FAQ link in Contact page
            const faqLink = document.getElementById('faq-link');
            if (faqLink) {
                faqLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    showPage('home');
                    // Scroll to FAQ section
                    setTimeout(() => {
                        document.querySelector('.faq-question').scrollIntoView({ behavior: 'smooth' });
                    }, 100);
                });
            }
            
            // Toggle FAQ answers
            const faqQuestions = document.querySelectorAll('.faq-question');
            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    const answer = this.nextElementSibling;
                    const isHidden = answer.classList.contains('hidden');
                    answer.classList.toggle('hidden', !isHidden);
                    
                    const icon = this.querySelector('svg');
                    if (isHidden) {
                        icon.classList.remove('transform', 'rotate-180');
                    } else {
                        icon.classList.add('transform', 'rotate-180');
                    }
                });
            });
            
            // File Upload Functionality (Placeholder)
            setupFileDragDrop('merge-drop-area', 'merge-file-input', handleMergeFiles);
            setupFileDragDrop('compress-drop-area', 'compress-file-input', handleCompressFile);
            setupFileDragDrop('reorder-drop-area', 'reorder-file-input', handleReorderFile);
            
            // PDF Merge tool functionality
            const clearMergeFilesBtn = document.getElementById('clear-merge-files');
            const mergePdfButton = document.getElementById('merge-pdf-button');
            const mergeNewBtn = document.getElementById('merge-new-btn');
            
            if (clearMergeFilesBtn) {
                clearMergeFilesBtn.addEventListener('click', function() {
                    resetMergeTool();
                });
            }
            
            if (mergePdfButton) {
                mergePdfButton.addEventListener('click', function() {
                    processMergeFiles();
                });
            }
            
            if (mergeNewBtn) {
                mergeNewBtn.addEventListener('click', function() {
                    resetMergeTool();
                });
            }
            
            // PDF Compress tool functionality
            const clearCompressFileBtn = document.getElementById('clear-compress-file');
            const compressPdfButton = document.getElementById('compress-pdf-button');
            const compressNewBtn = document.getElementById('compress-new-btn');
            
            if (clearCompressFileBtn) {
                clearCompressFileBtn.addEventListener('click', function() {
                    resetCompressTool();
                });
            }
            
            if (compressPdfButton) {
                compressPdfButton.addEventListener('click', function() {
                    processCompressFile();
                });
            }
            
            if (compressNewBtn) {
                compressNewBtn.addEventListener('click', function() {
                    resetCompressTool();
                });
            }
            
            // PDF Reorder tool functionality
            const clearReorderFileBtn = document.getElementById('clear-reorder-file');
            const reorderPdfButton = document.getElementById('reorder-pdf-button');
            const reorderNewBtn = document.getElementById('reorder-new-btn');
            const applyManualOrderBtn = document.getElementById('apply-manual-order');
            
            if (clearReorderFileBtn) {
                clearReorderFileBtn.addEventListener('click', function() {
                    resetReorderTool();
                });
            }
            
            if (reorderPdfButton) {
                reorderPdfButton.addEventListener('click', function() {
                    processReorderFile();
                });
            }
            
            if (reorderNewBtn) {
                reorderNewBtn.addEventListener('click', function() {
                    resetReorderTool();
                });
            }
            
            if (applyManualOrderBtn) {
                applyManualOrderBtn.addEventListener('click', function() {
                    applyManualOrder();
                });
            }
            
            // Contact form submission
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitContactForm();
                });
            }
            
            // Set up initial page
            showPage('home');
        });
        
        // File Upload Handlers
        function setupFileDragDrop(dropAreaId, fileInputId, handleFunction) {
            const dropArea = document.getElementById(dropAreaId);
            const fileInput = document.getElementById(fileInputId);
            
            if (!dropArea || !fileInput) return;
            
            // Prevent default behavior
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // Highlight drop area when item is dragged over
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropArea.classList.add('is-active');
            }
            
            function unhighlight() {
                dropArea.classList.remove('is-active');
            }
            
            // Handle dropped files
            dropArea.addEventListener('drop', function(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFunction(files);
            });
            
            // Handle selected files
            fileInput.addEventListener('change', function() {
                handleFunction(this.files);
            });
        }
        
        // PDF Merge Tool Functions
        function handleMergeFiles(files) {
            if (files.length === 0) return;
            
            // Filter for PDF files
            const pdfFiles = Array.from(files).filter(file => file.type === 'application/pdf');
            
            if (pdfFiles.length === 0) {
                alert('الرجاء اختيار ملفات PDF فقط.');
                return;
            }
            
            // Show the preview area
            document.getElementById('merge-drop-area').style.display = 'none';
            document.getElementById('merge-preview').classList.remove('hidden');
            
            // Generate file previews
            const fileList = document.getElementById('merge-file-list');
            fileList.innerHTML = '';
            
            pdfFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.draggable = true;
                fileItem.dataset.index = index;
                
                fileItem.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span class="font-semibold text-lg mb-1">${index + 1}</span>
                        <button class="text-red-500 hover:text-red-700" data-delete="${index}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                    <div class="bg-white dark:bg-gray-700 rounded-md p-2 mb-2 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <p class="text-sm truncate" title="${file.name}">${file.name}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">${formatFileSize(file.size)}</p>
                `;
                
                fileList.appendChild(fileItem);
                
                // Add delete button event
                const deleteBtn = fileItem.querySelector(`[data-delete="${index}"]`);
                deleteBtn.addEventListener('click', function() {
                    fileItem.remove();
                    updateFileOrder();
                    
                    // If no files left, show the drop area again
                    if (fileList.children.length === 0) {
                        resetMergeTool();
                    }
                });
            });
            
            // Set up drag and drop reordering
            setupDragAndDropReordering(fileList);
        }
        
        function setupDragAndDropReordering(container) {
            let draggedItem = null;
            
            // Add event listeners to all items
            Array.from(container.children).forEach(item => {
                item.addEventListener('dragstart', function() {
                    draggedItem = this;
                    setTimeout(() => this.classList.add('opacity-50'), 0);
                });
                
                item.addEventListener('dragend', function() {
                    this.classList.remove('opacity-50');
                    draggedItem = null;
                });
                
                item.addEventListener('dragover', function(e) {
                    e.preventDefault();
                });
                
                item.addEventListener('dragenter', function(e) {
                    e.preventDefault();
                    if (this !== draggedItem) {
                        this.classList.add('border-2', 'border-primary');
                    }
                });
                
                item.addEventListener('dragleave', function() {
                    this.classList.remove('border-2', 'border-primary');
                });
                
                item.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('border-2', 'border-primary');
                    
                    if (this !== draggedItem) {
                        const allItems = Array.from(container.children);
                        const draggedIndex = allItems.indexOf(draggedItem);
                        const targetIndex = allItems.indexOf(this);
                        
                        if (draggedIndex < targetIndex) {
                            container.insertBefore(draggedItem, this.nextSibling);
                        } else {
                            container.insertBefore(draggedItem, this);
                        }
                        
                        updateFileOrder();
                    }
                });
            });
        }
        
        function updateFileOrder() {
            const fileItems = document.querySelectorAll('#merge-file-list .file-item');
            fileItems.forEach((item, index) => {
                const numberSpan = item.querySelector('.font-semibold');
                if (numberSpan) {
                    numberSpan.textContent = index + 1;
                }
            });
        }
        
        function processMergeFiles() {
            // Show processing state
            document.getElementById('merge-preview').classList.add('hidden');
            document.getElementById('merge-processing').classList.remove('hidden');
            
            // Simulate progress
            simulateProgress('merge-progress-bar', 'merge-progress-text', function() {
                // Show success state
                document.getElementById('merge-processing').classList.add('hidden');
                document.getElementById('merge-success').classList.remove('hidden');
                
                // Set up download button (dummy for now)
                const downloadBtn = document.getElementById('merge-download-btn');
                downloadBtn.addEventListener('click', function() {
                    alert('تم تنفيذ عملية التحميل في واجهة المستخدم فقط. في التطبيق النهائي، سيتم تنزيل الملف المدمج هنا.');
                });
            });
        }
        
        function resetMergeTool() {
            document.getElementById('merge-drop-area').style.display = 'flex';
            document.getElementById('merge-preview').classList.add('hidden');
            document.getElementById('merge-processing').classList.add('hidden');
            document.getElementById('merge-success').classList.add('hidden');
            document.getElementById('merge-file-list').innerHTML = '';
            document.getElementById('merge-file-input').value = '';
        }
        
        // PDF Compress Tool Functions
        function handleCompressFile(files) {
            if (files.length === 0) return;
            
            // Take only the first file and check if it's a PDF
            const file = files[0];
            if (file.type !== 'application/pdf') {
                alert('الرجاء اختيار ملف PDF فقط.');
                return;
            }
            
            // Show the options area
            document.getElementById('compress-drop-area').style.display = 'none';
            document.getElementById('compress-options').classList.remove('hidden');
            
            // Set file details
            document.getElementById('compress-filename').textContent = file.name;
            document.getElementById('compress-filesize').textContent = formatFileSize(file.size);
        }
        
        function processCompressFile() {
            // Get selected compression level
            const compressionLevel = document.querySelector('input[name="compression-level"]:checked').value;
            
            // Show processing state
            document.getElementById('compress-options').classList.add('hidden');
            document.getElementById('compress-processing').classList.remove('hidden');
            
            // Simulate progress
            simulateProgress('compress-progress-bar', 'compress-progress-text', function() {
                // Calculate "compression" results (simulated)
                const originalSize = document.getElementById('compress-filesize').textContent;
                const originalSizeBytes = parseFloat(originalSize) * (originalSize.includes('MB') ? 1024 * 1024 : 1024);
                
                let compressionRatio;
                switch (compressionLevel) {
                    case 'low':
                        compressionRatio = 0.7;
                        break;
                    case 'medium':
                        compressionRatio = 0.5;
                        break;
                    case 'high':
                        compressionRatio = 0.3;
                        break;
                    default:
                        compressionRatio = 0.5;
                }
                
                const newSizeBytes = originalSizeBytes * compressionRatio;
                const reductionPercentage = Math.round((1 - compressionRatio) * 100);
                
                // Update result info
                document.getElementById('original-size').textContent = originalSize;
                document.getElementById('new-size').textContent = formatFileSize(newSizeBytes);
                document.getElementById('reduction-percentage').textContent = `${reductionPercentage}%`;
                
                // Show success state
                document.getElementById('compress-processing').classList.add('hidden');
                document.getElementById('compress-success').classList.remove('hidden');
                
                // Set up download button (dummy for now)
                const downloadBtn = document.getElementById('compress-download-btn');
                downloadBtn.addEventListener('click', function() {
                    alert('تم تنفيذ عملية التحميل في واجهة المستخدم فقط. في التطبيق النهائي، سيتم تنزيل الملف المضغوط هنا.');
                });
            });
        }
        
        function resetCompressTool() {
            document.getElementById('compress-drop-area').style.display = 'flex';
            document.getElementById('compress-options').classList.add('hidden');
            document.getElementById('compress-processing').classList.add('hidden');
            document.getElementById('compress-success').classList.add('hidden');
            document.getElementById('compress-file-input').value = '';
        }
        
        // PDF Reorder Tool Functions
        function handleReorderFile(files) {
            if (files.length === 0) return;
            
            // Take only the first file and check if it's a PDF
            const file = files[0];
            if (file.type !== 'application/pdf') {
                alert('الرجاء اختيار ملف PDF فقط.');
                return;
            }
            
            // Show the preview area
            document.getElementById('reorder-drop-area').style.display = 'none';
            document.getElementById('reorder-preview').classList.remove('hidden');
            
            // Generate random number of pages for the demo (5-10)
            const pageCount = Math.floor(Math.random() * 6) + 5;
            document.getElementById('reorder-pagecount').textContent = `${pageCount} صفحات`;
            
            // Generate page thumbnails
            const pagesContainer = document.getElementById('pages-container');
            pagesContainer.innerHTML = '';
            
            for (let i = 0; i < pageCount; i++) {
                const pageDiv = document.createElement('div');
                pageDiv.className = 'pdf-page';
                pageDiv.draggable = true;
                pageDiv.dataset.pageIndex = i + 1;
                
                // Create a pseudo page with a random color
                const hue = (i * 35) % 360; // Create distinct but not random colors
                pageDiv.style.backgroundColor = `hsl(${hue}, 70%, 85%)`;
                
                // Add page number
                const pageNumber = document.createElement('div');
                pageNumber.className = 'pdf-page-number';
                pageNumber.textContent = `صفحة ${i + 1}`;
                pageDiv.appendChild(pageNumber);
                
                pagesContainer.appendChild(pageDiv);
            }
            
            // Set up initial order in the manual input field
            const manualOrderInput = document.getElementById('manual-order');
            const initialOrder = Array.from({length: pageCount}, (_, i) => i + 1).join(',');
            manualOrderInput.value = initialOrder;
            
            // Set up drag and drop reordering for pages
            setupPageDragAndDrop(pagesContainer);
        }
        
        function setupPageDragAndDrop(container) {
            let draggedItem = null;
            
            // Add event listeners to all pages
            Array.from(container.children).forEach(page => {
                page.addEventListener('dragstart', function() {
                    draggedItem = this;
                    setTimeout(() => this.classList.add('opacity-50'), 0);
                });
                
                page.addEventListener('dragend', function() {
                    this.classList.remove('opacity-50');
                    draggedItem = null;
                    updateManualOrderInput();
                });
                
                page.addEventListener('dragover', function(e) {
                    e.preventDefault();
                });
                
                page.addEventListener('dragenter', function(e) {
                    e.preventDefault();
                    if (this !== draggedItem) {
                        this.classList.add('border-2', 'border-purple-500');
                    }
                });
                
                page.addEventListener('dragleave', function() {
                    this.classList.remove('border-2', 'border-purple-500');
                });
                
                page.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('border-2', 'border-purple-500');
                    
                    if (this !== draggedItem) {
                        const allPages = Array.from(container.children);
                        const draggedIndex = allPages.indexOf(draggedItem);
                        const targetIndex = allPages.indexOf(this);
                        
                        if (draggedIndex < targetIndex) {
                            container.insertBefore(draggedItem, this.nextSibling);
                        } else {
                            container.insertBefore(draggedItem, this);
                        }
                    }
                });
            });
        }
        
        function updateManualOrderInput() {
            const pages = Array.from(document.querySelectorAll('#pages-container .pdf-page'));
            const pageOrder = pages.map(page => page.dataset.pageIndex).join(',');
            document.getElementById('manual-order').value = pageOrder;
        }
        
        function applyManualOrder() {
            const manualInput = document.getElementById('manual-order').value;
            const orderArray = manualInput.split(',').map(num => parseInt(num.trim()));
            
            // Validate input
            const pageCount = document.querySelectorAll('#pages-container .pdf-page').length;
            const isValid = orderArray.length === pageCount && 
                            orderArray.every(num => !isNaN(num) && num >= 1 && num <= pageCount) &&
                            new Set(orderArray).size === pageCount;
            
            if (!isValid) {
                alert(`الرجاء إدخال ترتيب صحيح يحتوي على جميع أرقام الصفحات من 1 إلى ${pageCount} بدون تكرار.`);
                return;
            }
            
            // Rearrange pages based on the manual input
            const pagesContainer = document.getElementById('pages-container');
            const pages = Array.from(document.querySelectorAll('#pages-container .pdf-page'));
            
            // Clear container
            pagesContainer.innerHTML = '';
            
            // Add pages in the new order
            orderArray.forEach(pageNum => {
                const page = pages.find(p => parseInt(p.dataset.pageIndex) === pageNum);
                if (page) {
                    pagesContainer.appendChild(page);
                }
            });
        }
        
        function processReorderFile() {
            // Show processing state
            document.getElementById('reorder-preview').classList.add('hidden');
            document.getElementById('reorder-processing').classList.remove('hidden');
            
            // Simulate progress
            simulateProgress('reorder-progress-bar', 'reorder-progress-text', function() {
                // Show success state
                document.getElementById('reorder-processing').classList.add('hidden');
                document.getElementById('reorder-success').classList.remove('hidden');
                
                // Set up download button (dummy for now)
                const downloadBtn = document.getElementById('reorder-download-btn');
                downloadBtn.addEventListener('click', function() {
                    alert('تم تنفيذ عملية التحميل في واجهة المستخدم فقط. في التطبيق النهائي، سيتم تنزيل الملف المعاد ترتيبه هنا.');
                });
            });
        }
        
        function resetReorderTool() {
            document.getElementById('reorder-drop-area').style.display = 'flex';
            document.getElementById('reorder-preview').classList.add('hidden');
            document.getElementById('reorder-processing').classList.add('hidden');
            document.getElementById('reorder-success').classList.add('hidden');
            document.getElementById('reorder-file-input').value = '';
            document.getElementById('pages-container').innerHTML = '';
        }
        
        // Contact Form Functions
        function submitContactForm() {
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;
            
            // Simple validation
            if (!name || !email || !subject || !message) {
                alert('الرجاء ملء جميع الحقول المطلوبة.');
                return;
            }
            
            // Simulate form submission
            document.getElementById('contact-form').style.display = 'none';
            document.getElementById('contact-success').classList.remove('hidden');
            
            // Reset form
            document.getElementById('name').value = '';
            document.getElementById('email').value = '';
            document.getElementById('subject').value = '';
            document.getElementById('message').value = '';
            
            // After 5 seconds, hide success message and show form again
            setTimeout(() => {
                document.getElementById('contact-form').style.display = 'block';
                document.getElementById('contact-success').classList.add('hidden');
            }, 5000);
        }
        
        // Utility Functions
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function simulateProgress(progressBarId, progressTextId, callback) {
            const progressBar = document.getElementById(progressBarId);
            const progressText = document.getElementById(progressTextId);
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    setTimeout(callback, 500);
                }
                
                progressBar.style.width = `${progress}%`;
                progressText.textContent = `${Math.round(progress)}%`;
            }, 200);
        }
    </script>
</body>
</html>
